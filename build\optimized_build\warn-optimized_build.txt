
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

excluded module named zipfile - imported by shutil (delayed), importlib.resources.readers (top-level), importlib.metadata (top-level), D:\Program Files\anaconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_inspect.py (delayed)
missing module named pyimod02_importers - imported by D:\Program Files\anaconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
excluded module named pickle - imported by tracemalloc (top-level), logging (delayed, conditional), pycparser.ply.yacc (delayed, optional), pdfminer.cmapdb (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named 'email.message' - imported by importlib.metadata._adapters (top-level), cgi (top-level)
excluded module named email - imported by importlib.metadata (top-level)
excluded module named csv - imported by importlib.metadata (top-level), pdfplumber.container (top-level)
missing module named org - imported by copy (optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named StringIO - imported by Crypto.Util.py3compat (conditional), six (conditional)
excluded module named tarfile - imported by shutil (delayed), dateutil.zoneinfo (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level)
missing module named 'urllib.parse' - imported by pathlib (top-level), xml.dom.xmlbuilder (delayed), xml.sax.saxutils (top-level), xml.etree.ElementInclude (top-level), lxml.ElementInclude (optional), cgi (top-level), lxml.html (delayed, optional), 'urllib.parse' (top-level), lxml.html.clean (optional), lxml.html.html5parser (optional)
missing module named 'http.client' - imported by 'http.client' (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), webbrowser (delayed)
missing module named grp - imported by shutil (delayed, optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional)
excluded module named lzma - imported by shutil (optional)
excluded module named bz2 - imported by shutil (optional), encodings.bz2_codec (top-level)
missing module named 'urllib.request' - imported by xml.dom.xmlbuilder (delayed, conditional), xml.sax.saxutils (top-level), lxml.ElementInclude (optional), lxml.html (delayed, optional), lxml.html.html5parser (optional)
missing module named 'org.python' - imported by xml.sax (delayed, conditional)
missing module named java - imported by xml.sax._exceptions (conditional)
excluded module named gzip - imported by pdfminer.cmapdb (top-level), lxml.etree (top-level)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named 'html.entities' - imported by bs4.dammit (top-level), lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional), bs4.element (optional), bs4.builder._lxml (optional)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named bs4.builder.HTMLParserTreeBuilder - imported by bs4.builder (top-level), bs4 (top-level)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (optional), bs4.builder._html5lib (optional), lxml.html._html5builder (top-level)
missing module named collections.MutableMapping - imported by collections (optional), lxml.html (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional)
missing module named collections.Mapping - imported by collections (optional), html5lib._utils (optional), html5lib._trie._base (optional)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named 'html.parser' - imported by bs4.builder._htmlparser (top-level)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html (optional), lxml.html.clean (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named 'email.parser' - imported by cgi (top-level)
excluded module named html - imported by pdfminer.utils (top-level), lxml.doctestcompare (optional), cgi (top-level), lxml.html.diff (optional)
excluded module named urllib - imported by lxml.html (delayed, optional), lxml.html.clean (optional)
missing module named collections.MutableSet - imported by collections (optional), lxml.html (optional), lxml.html._setmixin (optional)
excluded module named socket - imported by platform (delayed, optional), uuid (delayed), webbrowser (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named fcntl - imported by subprocess (optional)
excluded module named doctest - imported by lxml.doctestcompare (top-level)
missing module named 'pandas.core' - imported by pdfplumber.utils.generic (conditional), pdfplumber.display (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named _winreg - imported by platform (delayed, optional)
excluded module named multiprocessing - imported by pypdfium2._helpers.document (top-level)
excluded module named numpy - imported by pypdfium2._helpers.bitmap (optional)
missing module named 'PIL.ImageDraw' - imported by pdfplumber.display (top-level)
missing module named 'PIL.Image' - imported by pdfplumber.display (top-level), pypdfium2._helpers.bitmap (optional), pypdfium2._helpers.pageobjects (optional)
excluded module named PIL - imported by PyPDF2.filters (delayed, optional), pdfminer.image (delayed, conditional, optional)
missing module named 'asyncio.coroutines' - imported by typing_extensions (delayed, conditional)
missing module named 'email.utils' - imported by cryptography.x509.general_name (top-level)
missing module named pygame - imported by pdfminer.ccitt (delayed)
excluded module named unittest - imported by pdfminer.ccitt (delayed, conditional)
missing module named 'distutils.command' - imported by cffi.recompiler (delayed)
missing module named 'distutils.ccompiler' - imported by cffi.recompiler (delayed, conditional)
missing module named 'distutils.msvc9compiler' - imported by cffi.recompiler (delayed, conditional)
missing module named 'distutils.dir_util' - imported by cffi.api (delayed)
excluded module named distutils - imported by cffi.api (delayed, conditional, optional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
excluded module named setuptools - imported by cffi.ffiplatform (delayed, conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named 'distutils.log' - imported by cffi.ffiplatform (delayed)
missing module named 'distutils.errors' - imported by cffi.ffiplatform (delayed)
missing module named 'distutils.core' - imported by cffi.ffiplatform (delayed)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
excluded module named tkinter.commondialog - imported by tkinter (top-level), tkinter.filedialog (top-level), tkinter.messagebox (top-level)
excluded module named tkinter.simpledialog - imported by tkinter.filedialog (top-level)
