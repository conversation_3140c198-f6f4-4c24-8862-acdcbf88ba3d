"""
发票重命名工具 - 图形界面版本
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
from pathlib import Path
import queue
import time

# 导入处理模块
from pdf_processor import PDFProcessor
from xml_processor import XMLProcessor
from config import SUPPORTED_PDF_EXTENSIONS, SUPPORTED_XML_EXTENSIONS

class InvoiceRenamerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("发票重命名工具 v2.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化处理器
        self.pdf_processor = PDFProcessor()
        self.xml_processor = XMLProcessor()
        
        # 线程通信队列
        self.message_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        
        # 启动消息处理
        self.process_queue()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="发票重命名工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 输入目录选择
        ttk.Label(main_frame, text="输入目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.input_dir_var = tk.StringVar(value=os.path.join(os.getcwd(), "input"))
        input_entry = ttk.Entry(main_frame, textvariable=self.input_dir_var, width=50)
        input_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_input_dir).grid(row=1, column=2, pady=5)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.output_dir_var = tk.StringVar(value=os.path.join(os.getcwd(), "output"))
        output_entry = ttk.Entry(main_frame, textvariable=self.output_dir_var, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_output_dir).grid(row=2, column=2, pady=5)
        
        # 选项框架
        options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(0, weight=1)
        
        # 文件类型选择
        self.file_type_var = tk.StringVar(value="all")
        type_frame = ttk.Frame(options_frame)
        type_frame.grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(type_frame, text="文件类型:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="全部", variable=self.file_type_var, value="all").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(type_frame, text="仅PDF", variable=self.file_type_var, value="pdf").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(type_frame, text="仅XML", variable=self.file_type_var, value="xml").pack(side=tk.LEFT, padx=5)
        
        # 预览模式
        self.preview_mode_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="预览模式（不实际重命名文件）", 
                       variable=self.preview_mode_var).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始处理", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="打开输出目录", command=self.open_output_dir).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=7, column=0, columnspan=3, pady=5)
    
    def browse_input_dir(self):
        """浏览输入目录"""
        directory = filedialog.askdirectory(title="选择输入目录", initialdir=self.input_dir_var.get())
        if directory:
            self.input_dir_var.set(directory)
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录", initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def open_output_dir(self):
        """打开输出目录"""
        output_dir = self.output_dir_var.get()
        if os.path.exists(output_dir):
            os.startfile(output_dir)
        else:
            messagebox.showwarning("警告", "输出目录不存在")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_processing(self):
        """开始处理"""
        input_dir = self.input_dir_var.get()
        output_dir = self.output_dir_var.get()
        
        # 验证输入
        if not os.path.exists(input_dir):
            messagebox.showerror("错误", "输入目录不存在")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 禁用开始按钮
        self.start_button.config(state="disabled")
        self.progress_var.set(0)
        self.status_var.set("正在处理...")
        
        # 在新线程中处理
        thread = threading.Thread(target=self.process_files, daemon=True)
        thread.start()
    
    def process_files(self):
        """处理文件（在后台线程中运行）"""
        try:
            input_dir = self.input_dir_var.get()
            output_dir = self.output_dir_var.get()
            file_type = self.file_type_var.get()
            preview_mode = self.preview_mode_var.get()
            
            self.message_queue.put(("log", "开始扫描文件..."))
            
            # 获取文件列表
            pdf_files = []
            xml_files = []
            
            input_path = Path(input_dir)
            subdirs = [d for d in input_path.iterdir() if d.is_dir()]
            
            if subdirs:
                self.message_queue.put(("log", f"发现 {len(subdirs)} 个子文件夹"))
                for subdir in subdirs:
                    folder_name = subdir.name
                    self.message_queue.put(("log", f"扫描文件夹: {folder_name}"))
                    
                    folder_pdf_count = 0
                    folder_xml_count = 0
                    
                    for file_path in subdir.rglob('*'):
                        if file_path.is_file():
                            ext = file_path.suffix.lower()
                            if ext in SUPPORTED_PDF_EXTENSIONS and file_type in ['pdf', 'all']:
                                pdf_files.append((str(file_path), folder_name))
                                folder_pdf_count += 1
                            elif ext in SUPPORTED_XML_EXTENSIONS and file_type in ['xml', 'all']:
                                xml_files.append((str(file_path), folder_name))
                                folder_xml_count += 1
                    
                    if folder_pdf_count > 0 or folder_xml_count > 0:
                        self.message_queue.put(("log", f"  ✓ {folder_name}: {folder_pdf_count} PDF, {folder_xml_count} XML"))
                    else:
                        self.message_queue.put(("log", f"  - {folder_name}: 无可处理文件"))
            else:
                self.message_queue.put(("log", "处理根目录文件"))
                for file_path in input_path.iterdir():
                    if file_path.is_file():
                        ext = file_path.suffix.lower()
                        if ext in SUPPORTED_PDF_EXTENSIONS and file_type in ['pdf', 'all']:
                            pdf_files.append((str(file_path), ""))
                        elif ext in SUPPORTED_XML_EXTENSIONS and file_type in ['xml', 'all']:
                            xml_files.append((str(file_path), ""))
            
            total_files = len(pdf_files) + len(xml_files)
            self.message_queue.put(("log", f"总共找到 {len(pdf_files)} 个PDF文件和 {len(xml_files)} 个XML文件"))
            
            if total_files == 0:
                self.message_queue.put(("log", "没有找到可处理的文件"))
                self.message_queue.put(("status", "完成"))
                self.message_queue.put(("button", True))
                return
            
            success_count = 0
            current_file = 0
            
            # 处理PDF文件
            if pdf_files:
                self.message_queue.put(("log", f"\n开始处理 {len(pdf_files)} 个PDF文件..."))
                for pdf_file, folder_name in pdf_files:
                    current_file += 1
                    progress = (current_file / total_files) * 100
                    self.message_queue.put(("progress", progress))
                    
                    file_name = os.path.basename(pdf_file)
                    folder_info = f" (来自: {folder_name})" if folder_name else ""
                    self.message_queue.put(("log", f"[{current_file}/{total_files}] 处理: {file_name}{folder_info}"))
                    
                    if preview_mode:
                        year_month, invoice_number, amount = self.pdf_processor.extract_invoice_info(pdf_file)
                        if all([year_month, invoice_number, amount]):
                            prefix = f"{folder_name}+" if folder_name else ""
                            new_name = f"{prefix}{year_month}+{invoice_number}+{amount}.pdf"
                            self.message_queue.put(("log", f"  预览: {new_name}"))
                            success_count += 1
                        else:
                            self.message_queue.put(("log", f"  ✗ 无法提取完整信息"))
                    else:
                        if self.pdf_processor.rename_pdf_file(pdf_file, output_dir, folder_name):
                            success_count += 1
                            self.message_queue.put(("log", f"  ✓ 重命名成功"))
                        else:
                            self.message_queue.put(("log", f"  ✗ 重命名失败"))
            
            # 处理XML文件
            if xml_files:
                self.message_queue.put(("log", f"\n开始处理 {len(xml_files)} 个XML文件..."))
                for xml_file, folder_name in xml_files:
                    current_file += 1
                    progress = (current_file / total_files) * 100
                    self.message_queue.put(("progress", progress))
                    
                    file_name = os.path.basename(xml_file)
                    folder_info = f" (来自: {folder_name})" if folder_name else ""
                    self.message_queue.put(("log", f"[{current_file}/{total_files}] 处理: {file_name}{folder_info}"))
                    
                    if preview_mode:
                        seller_name, amount, date = self.xml_processor.extract_xml_info(xml_file)
                        if all([seller_name, amount, date]):
                            prefix = f"{folder_name}+" if folder_name else ""
                            new_name = f"{prefix}{seller_name}+{amount}+{date}.xml"
                            self.message_queue.put(("log", f"  预览: {new_name}"))
                            success_count += 1
                        else:
                            self.message_queue.put(("log", f"  ✗ 无法提取完整信息"))
                    else:
                        if self.xml_processor.rename_xml_file(xml_file, output_dir, folder_name):
                            success_count += 1
                            self.message_queue.put(("log", f"  ✓ 重命名成功"))
                        else:
                            self.message_queue.put(("log", f"  ✗ 重命名失败"))
            
            # 完成
            self.message_queue.put(("progress", 100))
            self.message_queue.put(("log", f"\n处理完成！"))
            self.message_queue.put(("log", f"总文件数: {total_files}"))
            self.message_queue.put(("log", f"成功处理: {success_count}"))
            self.message_queue.put(("log", f"失败数量: {total_files - success_count}"))
            
            if not preview_mode and success_count > 0:
                self.message_queue.put(("log", f"文件保存在: {output_dir}"))
            
            self.message_queue.put(("status", "完成"))
            
        except Exception as e:
            self.message_queue.put(("log", f"处理过程中出错: {str(e)}"))
            self.message_queue.put(("status", "出错"))
        finally:
            self.message_queue.put(("button", True))
    
    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                msg_type, msg_data = self.message_queue.get_nowait()
                
                if msg_type == "log":
                    self.log_message(msg_data)
                elif msg_type == "progress":
                    self.progress_var.set(msg_data)
                elif msg_type == "status":
                    self.status_var.set(msg_data)
                elif msg_type == "button":
                    self.start_button.config(state="normal" if msg_data else "disabled")
                    
        except queue.Empty:
            pass
        
        # 继续处理队列
        self.root.after(100, self.process_queue)

def main():
    """主函数"""
    root = tk.Tk()
    app = InvoiceRenamerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
