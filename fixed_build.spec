# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 最小化排除列表，只排除明确不需要的大型模块
excludes = [
    # 科学计算库
    'matplotlib', 'numpy', 'scipy', 'pandas', 'PIL', 'cv2',
    'torch', 'tensorflow', 'sklearn', 'seaborn', 'plotly',
    
    # 开发工具
    'jupyter', 'IPython', 'notebook', 'qtconsole', 'spyder',
    'anaconda_navigator', 'conda',
    
    # 测试框架
    'unittest', 'doctest', 'test', 'tests', 'testing',
    
    # 大型网络模块
    'multiprocessing', 'concurrent', 'asyncio',
    
    # 音频
    'wave', 'audioop', 'chunk', 'sunau', 'aifc', 'sndhdr',
    'ossaudiodev', 'winsound',
    
    # 图形界面额外组件（保留必要的对话框组件）
    'turtle', 'tkinter.dnd', 'tkinter.colorchooser',
    'tkinter.simpledialog', 'tkinter.font', 'tkinter.tix',
]

a = Analysis(
    ['gui_main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        # GUI相关
        'tkinter',
        'tkinter.ttk', 
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        
        # PDF和XML处理
        'PyPDF2',
        'pdfplumber',
        'lxml.etree',
        'xml.etree.ElementTree',
        
        # 必要的Python核心模块
        'threading',
        'queue',
        'pathlib',
        'shutil',
        'dateutil.parser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 简化的过滤器，只过滤明显不需要的文件
def filter_binaries(binaries):
    filtered = []
    exclude_patterns = [
        'matplotlib', 'numpy', 'scipy', 'pandas', 'torch', 
        'tensorflow', 'mkl', 'blas', 'lapack'
    ]
    
    for binary in binaries:
        name = binary[0].lower()
        if not any(pattern in name for pattern in exclude_patterns):
            filtered.append(binary)
    
    return filtered

# 应用过滤器
a.binaries = filter_binaries(a.binaries)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='发票重命名工具_修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,  # 禁用strip避免问题
    upx=True,     # 保留UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
