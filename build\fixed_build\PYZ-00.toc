('D:\\utils\\RenameReceipt\\build\\fixed_build\\PYZ-00.pyz',
 [('Crypto',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC4',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\ARC4.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Random',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('PyPDF2',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._cmap',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.xmp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\xmp.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Program Files\\anaconda3\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files\\anaconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Program Files\\anaconda3\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files\\anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Program Files\\anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase',
   'D:\\Program Files\\anaconda3\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Program Files\\anaconda3\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Program Files\\anaconda3\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files\\anaconda3\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Program Files\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Program Files\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Program Files\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Program Files\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bisect', 'D:\\Program Files\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bs4',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\Program Files\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Program Files\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('cffi',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\Program Files\\anaconda3\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('config', 'D:\\utils\\RenameReceipt\\config.py', 'PYMODULE'),
  ('configparser',
   'D:\\Program Files\\anaconda3\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Program Files\\anaconda3\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Program Files\\anaconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cssselect',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files\\anaconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Program Files\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Program Files\\anaconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('email',
   'D:\\Program Files\\anaconda3\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files\\anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files\\anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files\\anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files\\anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files\\anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files\\anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files\\anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Program Files\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Program Files\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Program Files\\anaconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Program Files\\anaconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Program Files\\anaconda3\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Program Files\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Program Files\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files\\anaconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Program Files\\anaconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Program Files\\anaconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'D:\\Program Files\\anaconda3\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('html5lib',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\__init__.py',
   'PYMODULE'),
  ('html5lib._ihatexml',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('html5lib._inputstream',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('html5lib._tokenizer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('html5lib._trie',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('html5lib._trie._base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('html5lib._trie.py',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('html5lib._utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_utils.py',
   'PYMODULE'),
  ('html5lib.constants',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\constants.py',
   'PYMODULE'),
  ('html5lib.filters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('html5lib.filters.alphabeticalattributes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('html5lib.filters.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('html5lib.filters.inject_meta_charset',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('html5lib.filters.optionaltags',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('html5lib.filters.sanitizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('html5lib.filters.whitespace',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('html5lib.html5parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('html5lib.serializer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\serializer.py',
   'PYMODULE'),
  ('html5lib.treebuilders',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('html5lib.treebuilders.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('html5lib.treebuilders.dom',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree_lxml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('html5lib.treewalkers.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('html5lib.treewalkers.dom',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree_lxml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers.genshi',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('http', 'D:\\Program Files\\anaconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Program Files\\anaconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files\\anaconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Program Files\\anaconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('imp', 'D:\\Program Files\\anaconda3\\Lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Program Files\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Program Files\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files\\anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files\\anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files\\anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files\\anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Program Files\\anaconda3\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\Program Files\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Program Files\\anaconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'D:\\Program Files\\anaconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Program Files\\anaconda3\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Program Files\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\Program Files\\anaconda3\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Program Files\\anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdf_processor', 'D:\\utils\\RenameReceipt\\pdf_processor.py', 'PYMODULE'),
  ('pdfminer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\__init__.py',
   'PYMODULE'),
  ('pdfminer._saslprep',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\_saslprep.py',
   'PYMODULE'),
  ('pdfminer.arcfour',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\arcfour.py',
   'PYMODULE'),
  ('pdfminer.ascii85',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\ascii85.py',
   'PYMODULE'),
  ('pdfminer.ccitt',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\ccitt.py',
   'PYMODULE'),
  ('pdfminer.cmapdb',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmapdb.py',
   'PYMODULE'),
  ('pdfminer.converter',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\converter.py',
   'PYMODULE'),
  ('pdfminer.data_structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\data_structures.py',
   'PYMODULE'),
  ('pdfminer.encodingdb',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\encodingdb.py',
   'PYMODULE'),
  ('pdfminer.fontmetrics',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\fontmetrics.py',
   'PYMODULE'),
  ('pdfminer.glyphlist',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\glyphlist.py',
   'PYMODULE'),
  ('pdfminer.image',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\image.py',
   'PYMODULE'),
  ('pdfminer.jbig2',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\jbig2.py',
   'PYMODULE'),
  ('pdfminer.latin_enc',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\latin_enc.py',
   'PYMODULE'),
  ('pdfminer.layout',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\layout.py',
   'PYMODULE'),
  ('pdfminer.lzw',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\lzw.py',
   'PYMODULE'),
  ('pdfminer.pdfcolor',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfcolor.py',
   'PYMODULE'),
  ('pdfminer.pdfdevice',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfdevice.py',
   'PYMODULE'),
  ('pdfminer.pdfdocument',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfdocument.py',
   'PYMODULE'),
  ('pdfminer.pdffont',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdffont.py',
   'PYMODULE'),
  ('pdfminer.pdfinterp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfinterp.py',
   'PYMODULE'),
  ('pdfminer.pdfpage',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfpage.py',
   'PYMODULE'),
  ('pdfminer.pdfparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfparser.py',
   'PYMODULE'),
  ('pdfminer.pdftypes',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdftypes.py',
   'PYMODULE'),
  ('pdfminer.psparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\psparser.py',
   'PYMODULE'),
  ('pdfminer.runlength',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\runlength.py',
   'PYMODULE'),
  ('pdfminer.settings',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\settings.py',
   'PYMODULE'),
  ('pdfminer.utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\utils.py',
   'PYMODULE'),
  ('pdfplumber',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\__init__.py',
   'PYMODULE'),
  ('pdfplumber._typing',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\_typing.py',
   'PYMODULE'),
  ('pdfplumber._version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\_version.py',
   'PYMODULE'),
  ('pdfplumber.container',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\container.py',
   'PYMODULE'),
  ('pdfplumber.convert',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\convert.py',
   'PYMODULE'),
  ('pdfplumber.display',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\display.py',
   'PYMODULE'),
  ('pdfplumber.page',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\page.py',
   'PYMODULE'),
  ('pdfplumber.pdf',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\pdf.py',
   'PYMODULE'),
  ('pdfplumber.repair',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\repair.py',
   'PYMODULE'),
  ('pdfplumber.table',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\table.py',
   'PYMODULE'),
  ('pdfplumber.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.utils.clustering',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\clustering.py',
   'PYMODULE'),
  ('pdfplumber.utils.generic',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\generic.py',
   'PYMODULE'),
  ('pdfplumber.utils.geometry',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\geometry.py',
   'PYMODULE'),
  ('pdfplumber.utils.pdfinternals',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\pdfinternals.py',
   'PYMODULE'),
  ('pdfplumber.utils.text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\text.py',
   'PYMODULE'),
  ('pickle', 'D:\\Program Files\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Program Files\\anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Program Files\\anaconda3\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Program Files\\anaconda3\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Program Files\\anaconda3\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files\\anaconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files\\anaconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files\\anaconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pypdfium2',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers.attachment',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\attachment.py',
   'PYMODULE'),
  ('pypdfium2._helpers.bitmap',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\bitmap.py',
   'PYMODULE'),
  ('pypdfium2._helpers.document',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\document.py',
   'PYMODULE'),
  ('pypdfium2._helpers.matrix',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\matrix.py',
   'PYMODULE'),
  ('pypdfium2._helpers.misc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\misc.py',
   'PYMODULE'),
  ('pypdfium2._helpers.page',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\page.py',
   'PYMODULE'),
  ('pypdfium2._helpers.pageobjects',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\pageobjects.py',
   'PYMODULE'),
  ('pypdfium2._helpers.textpage',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\textpage.py',
   'PYMODULE'),
  ('pypdfium2._helpers.unsupported',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\unsupported.py',
   'PYMODULE'),
  ('pypdfium2._library_scope',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_library_scope.py',
   'PYMODULE'),
  ('pypdfium2.internal',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal.bases',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\bases.py',
   'PYMODULE'),
  ('pypdfium2.internal.consts',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\consts.py',
   'PYMODULE'),
  ('pypdfium2.internal.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\utils.py',
   'PYMODULE'),
  ('pypdfium2.raw',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\raw.py',
   'PYMODULE'),
  ('pypdfium2.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\version.py',
   'PYMODULE'),
  ('pypdfium2_raw',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2_raw\\__init__.py',
   'PYMODULE'),
  ('pypdfium2_raw.bindings',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2_raw\\bindings.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files\\anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Program Files\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('readline',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files\\anaconda3\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('selectors', 'D:\\Program Files\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files\\anaconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Program Files\\anaconda3\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib', 'D:\\Program Files\\anaconda3\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Program Files\\anaconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files\\anaconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Program Files\\anaconda3\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Program Files\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Program Files\\anaconda3\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Program Files\\anaconda3\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\Program Files\\anaconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Program Files\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Program Files\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Program Files\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Program Files\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\Program Files\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Program Files\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files\\anaconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files\\anaconda3\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Program Files\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('utils', 'D:\\utils\\RenameReceipt\\utils.py', 'PYMODULE'),
  ('uuid', 'D:\\Program Files\\anaconda3\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Program Files\\anaconda3\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('webencodings',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\webencodings\\__init__.py',
   'PYMODULE'),
  ('webencodings.labels',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\webencodings\\labels.py',
   'PYMODULE'),
  ('webencodings.x_user_defined',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('win32con',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\Program Files\\anaconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xml_processor', 'D:\\utils\\RenameReceipt\\xml_processor.py', 'PYMODULE'),
  ('zipfile', 'D:\\Program Files\\anaconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Program Files\\anaconda3\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE')])
