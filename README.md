# 发票重命名工具

这是一个 Python 工具，用于自动重命名 PDF 发票文件和 XML 文件。

## 功能特性

### PDF 发票处理

- 读取 PDF 发票文件
- 提取发票年份和月份、发票号、发票金额
- 重命名格式：`[文件夹名+]YYYYMM+发票号+发票金额.pdf`

### XML 文件处理

- 读取 XML 文件
- 提取 SellerName、发票金额、发票日期
- 重命名格式：`[文件夹名+]SellerName+发票金额+YYYYMMDD.xml`

### 子文件夹支持

- 支持在 input 目录中创建以人名命名的子文件夹
- 程序会自动遍历所有子文件夹中的文件
- 重命名后的文件会在文件名前添加对应的文件夹名作为前缀
- 所有重命名后的文件统一保存到 output 目录中

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

#### 方式一：子文件夹模式（推荐）

1. 在 `input` 目录中创建以人名命名的子文件夹
2. 将对应人员的 PDF 和 XML 文件放入相应的子文件夹中
3. 运行程序：

```bash
python main.py
```

#### 方式二：直接模式

1. 将要处理的 PDF 和 XML 文件直接放入 `input` 目录
2. 运行程序（文件名不会包含前缀）

处理后的文件将保存在 `output` 目录中。

#### 目录结构示例

```
input/
├── 张三/
│   ├── invoice1.pdf
│   └── invoice1.xml
├── 李四/
│   ├── invoice2.pdf
│   └── invoice2.xml
└── 王五/
    └── invoice3.xml
```

输出结果：

```
output/
├── 张三+202403+12345678+1250.00.pdf
├── 张三+北京科技有限公司+1250.00+20240315.xml
├── 李四+202404+87654321+2800.50.pdf
├── 李四+上海贸易公司+2800.50+20240420.xml
└── 王五+深圳制造公司+1500.00+20240510.xml
```

### 命令行参数

```bash
python main.py [选项]
```

可用选项：

- `--input-dir, -i`: 指定输入目录（默认：input）
- `--output-dir, -o`: 指定输出目录（默认：output）
- `--file-type, -t`: 指定处理的文件类型（pdf/xml/all，默认：all）
- `--dry-run`: 预览模式，只显示重命名结果不实际操作

### 使用示例

```bash
# 处理所有文件
python main.py

# 只处理PDF文件
python main.py --file-type pdf

# 指定自定义目录
python main.py --input-dir "D:/invoices" --output-dir "D:/renamed"

# 预览模式（不实际重命名）
python main.py --dry-run
```

## 目录结构

```
RenameReceipt/
├── main.py              # 主程序
├── pdf_processor.py     # PDF处理模块
├── xml_processor.py     # XML处理模块
├── utils.py             # 工具函数
├── config.py            # 配置文件
├── requirements.txt     # 依赖包
├── README.md           # 说明文档
├── input/              # 输入目录（放置原始文件）
└── output/             # 输出目录（重命名后的文件）
```

## 支持的文件格式

### PDF 文件

- 支持标准 PDF 格式
- 使用 pdfplumber 和 PyPDF2 双重解析
- 自动识别中英文发票格式

### XML 文件

- 支持标准 XML 格式
- 使用 lxml 和 ElementTree 双重解析
- 支持多种编码格式

## 提取的信息

### PDF 发票信息

- **年月**: 从发票日期提取 YYYYMM 格式
- **发票号**: 发票号码或发票代码
- **金额**: 合计金额、总金额或应付金额

### XML 文件信息

- **SellerName**: `<SellerName>` 标签内容
- **发票金额**: `<TotalTax-includedAmount>` 标签内容
- **发票日期**: `<RequestTime>` 标签内容（提取年月日）

## 注意事项

1. 程序会自动创建 `input` 和 `output` 目录
2. 如果目标文件名已存在，会自动添加序号避免覆盖
3. 文件名中的非法字符会被自动替换为下划线
4. 原始文件不会被修改，重命名后的文件是副本
5. 建议先使用 `--dry-run` 参数预览结果

## 错误处理

- 如果无法提取完整信息，程序会显示详细的错误信息
- 支持多种 PDF 和 XML 解析方式，提高成功率
- 程序会继续处理其他文件，不会因单个文件错误而停止

## 扩展性

代码采用模块化设计，可以轻松扩展：

- 添加新的文件格式支持
- 修改重命名规则
- 添加新的信息提取模式
