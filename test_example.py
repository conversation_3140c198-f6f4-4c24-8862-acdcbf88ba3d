"""
测试示例脚本
用于验证项目功能
"""
import os
from pdf_processor import PDFProcessor
from xml_processor import XMLProcessor

def create_sample_xml():
    """创建示例XML文件用于测试"""
    # 创建子文件夹
    os.makedirs('input/张三', exist_ok=True)
    os.makedirs('input/李四', exist_ok=True)

    # 张三的发票
    sample_xml_content1 = '''<?xml version="1.0" encoding="UTF-8"?>
<Invoice>
    <SellerName>北京科技有限公司</SellerName>
    <TotalTax-includedAmount>1250.00</TotalTax-includedAmount>
    <RequestTime>2024-03-15</RequestTime>
    <InvoiceNumber>12345678</InvoiceNumber>
</Invoice>'''

    with open('input/张三/invoice1.xml', 'w', encoding='utf-8') as f:
        f.write(sample_xml_content1)
    print("已创建示例XML文件: input/张三/invoice1.xml")

    # 李四的发票
    sample_xml_content2 = '''<?xml version="1.0" encoding="UTF-8"?>
<Invoice>
    <SellerName>上海贸易公司</SellerName>
    <TotalTax-includedAmount>2800.50</TotalTax-includedAmount>
    <RequestTime>2024-04-20</RequestTime>
    <InvoiceNumber>87654321</InvoiceNumber>
</Invoice>'''

    with open('input/李四/invoice2.xml', 'w', encoding='utf-8') as f:
        f.write(sample_xml_content2)
    print("已创建示例XML文件: input/李四/invoice2.xml")

def test_xml_processing():
    """测试XML处理功能"""
    print("\n=== 测试XML处理功能（子文件夹模式）===")

    # 创建示例XML文件
    create_sample_xml()

    # 测试XML处理器
    xml_processor = XMLProcessor()

    # 测试张三的发票
    xml_path1 = 'input/张三/invoice1.xml'
    if os.path.exists(xml_path1):
        print(f"\n测试文件: {xml_path1}")
        seller_name, amount, date = xml_processor.extract_xml_info(xml_path1)
        print(f"提取的信息:")
        print(f"  SellerName: {seller_name}")
        print(f"  金额: {amount}")
        print(f"  日期: {date}")

        if all([seller_name, amount, date]):
            print("✓ XML信息提取成功")
            # 测试重命名（带文件夹前缀）
            success = xml_processor.rename_xml_file(xml_path1, 'output', '张三')
            if success:
                print("✓ XML文件重命名成功（带文件夹前缀）")
            else:
                print("✗ XML文件重命名失败")
        else:
            print("✗ XML信息提取不完整")

    # 测试李四的发票
    xml_path2 = 'input/李四/invoice2.xml'
    if os.path.exists(xml_path2):
        print(f"\n测试文件: {xml_path2}")
        seller_name, amount, date = xml_processor.extract_xml_info(xml_path2)
        print(f"提取的信息:")
        print(f"  SellerName: {seller_name}")
        print(f"  金额: {amount}")
        print(f"  日期: {date}")

        if all([seller_name, amount, date]):
            print("✓ XML信息提取成功")
            # 测试重命名（带文件夹前缀）
            success = xml_processor.rename_xml_file(xml_path2, 'output', '李四')
            if success:
                print("✓ XML文件重命名成功（带文件夹前缀）")
            else:
                print("✗ XML文件重命名失败")
        else:
            print("✗ XML信息提取不完整")

def test_pdf_processing():
    """测试PDF处理功能（需要实际PDF文件）"""
    print("\n=== 测试PDF处理功能 ===")
    
    pdf_processor = PDFProcessor()
    
    # 查找input目录中的PDF文件
    pdf_files = []
    if os.path.exists('input'):
        for file in os.listdir('input'):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join('input', file))
    
    if pdf_files:
        for pdf_path in pdf_files:
            print(f"\n测试PDF文件: {pdf_path}")
            year_month, invoice_number, amount = pdf_processor.extract_invoice_info(pdf_path)
            print(f"提取的信息:")
            print(f"  年月: {year_month}")
            print(f"  发票号: {invoice_number}")
            print(f"  金额: {amount}")
            
            if all([year_month, invoice_number, amount]):
                print("✓ PDF信息提取成功")
                # 测试重命名
                success = pdf_processor.rename_pdf_file(pdf_path, 'output')
                if success:
                    print("✓ PDF文件重命名成功")
                else:
                    print("✗ PDF文件重命名失败")
            else:
                print("✗ PDF信息提取不完整")
    else:
        print("没有找到PDF文件进行测试")
        print("请将PDF发票文件放入input目录中进行测试")

def main():
    """主测试函数"""
    print("发票重命名工具测试")
    print("=" * 50)
    
    # 确保目录存在
    os.makedirs('input', exist_ok=True)
    os.makedirs('output', exist_ok=True)
    
    # 测试XML处理
    test_xml_processing()
    
    # 测试PDF处理
    test_pdf_processing()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n使用说明:")
    print("1. 在input目录中创建以人名命名的子文件夹")
    print("2. 将PDF和XML发票文件放入对应的子文件夹中")
    print("3. 运行: python main.py")
    print("4. 查看output目录中的重命名结果（文件名会包含文件夹前缀）")
    print("\n示例目录结构:")
    print("input/")
    print("  ├── 张三/")
    print("  │   ├── invoice1.pdf")
    print("  │   └── invoice1.xml")
    print("  └── 李四/")
    print("      ├── invoice2.pdf")
    print("      └── invoice2.xml")

if __name__ == "__main__":
    main()
