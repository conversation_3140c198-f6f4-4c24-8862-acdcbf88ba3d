"""
XML文件处理模块
"""
import os
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Optional, <PERSON>ple
from lxml import etree
from utils import sanitize_filename
from config import XML_FILENAME_FORMAT, DATE_FORMAT_YYYYMMDD

class XMLProcessor:
    def __init__(self):
        self.supported_extensions = ['.xml']
    
    def extract_xml_info(self, xml_path: str) -> <PERSON><PERSON>[Optional[str], Optional[str], Optional[str]]:
        """
        从XML中提取信息
        返回: (SellerName, 发票金额, 发票日期)
        """
        try:
            # 尝试使用lxml解析（更好的编码支持）
            try:
                with open(xml_path, 'rb') as file:
                    parser = etree.XMLParser(encoding='utf-8', recover=True)
                    tree = etree.parse(file, parser)
                    root = tree.getroot()
                
                seller_name = self._extract_seller_name_lxml(root)
                amount = self._extract_amount_lxml(root)
                date = self._extract_date_lxml(root)
                
            except Exception as e:
                print(f"lxml解析失败，尝试使用标准库: {str(e)}")
                # 如果lxml失败，使用标准库
                tree = ET.parse(xml_path)
                root = tree.getroot()
                
                seller_name = self._extract_seller_name_et(root)
                amount = self._extract_amount_et(root)
                date = self._extract_date_et(root)
            
            return seller_name, amount, date
            
        except Exception as e:
            print(f"处理XML文件时出错 {xml_path}: {str(e)}")
            return None, None, None
    
    def _extract_seller_name_lxml(self, root) -> Optional[str]:
        """使用lxml提取SellerName"""
        # 尝试多种可能的标签名和路径
        seller_patterns = [
            './/SellerName',
            './/sellername',
            './/SELLERNAME',
            './/Seller/Name',
            './/seller/name',
            './/销售方名称',
            './/卖方名称',
        ]
        
        for pattern in seller_patterns:
            elements = root.xpath(pattern)
            if elements and elements[0].text:
                return elements[0].text.strip()
        
        return None
    
    def _extract_seller_name_et(self, root) -> Optional[str]:
        """使用ElementTree提取SellerName"""
        seller_patterns = [
            'SellerName',
            'sellername',
            'SELLERNAME',
        ]
        
        for pattern in seller_patterns:
            element = root.find(f'.//{pattern}')
            if element is not None and element.text:
                return element.text.strip()
        
        return None
    
    def _extract_amount_lxml(self, root) -> Optional[str]:
        """使用lxml提取TotalTax-includedAmount"""
        amount_patterns = [
            './/TotalTax-includedAmount',
            './/TotalTaxIncludedAmount',
            './/totaltax-includedamount',
            './/TOTALTAX-INCLUDEDAMOUNT',
            './/含税总金额',
            './/总金额',
            './/TotalAmount',
            './/totalamount',
        ]
        
        for pattern in amount_patterns:
            elements = root.xpath(pattern)
            if elements and elements[0].text:
                amount_text = elements[0].text.strip()
                # 清理金额文本，移除货币符号和逗号
                amount_text = amount_text.replace('¥', '').replace('$', '').replace(',', '')
                try:
                    float(amount_text)  # 验证是否为有效数字
                    return amount_text
                except ValueError:
                    continue
        
        return None
    
    def _extract_amount_et(self, root) -> Optional[str]:
        """使用ElementTree提取TotalTax-includedAmount"""
        amount_patterns = [
            'TotalTax-includedAmount',
            'TotalTaxIncludedAmount',
            'totaltax-includedamount',
            'TOTALTAX-INCLUDEDAMOUNT',
            'TotalAmount',
            'totalamount',
        ]
        
        for pattern in amount_patterns:
            element = root.find(f'.//{pattern}')
            if element is not None and element.text:
                amount_text = element.text.strip()
                amount_text = amount_text.replace('¥', '').replace('$', '').replace(',', '')
                try:
                    float(amount_text)
                    return amount_text
                except ValueError:
                    continue
        
        return None
    
    def _extract_date_lxml(self, root) -> Optional[str]:
        """使用lxml提取RequestTime日期"""
        date_patterns = [
            './/RequestTime',
            './/requesttime',
            './/REQUESTTIME',
            './/开票日期',
            './/发票日期',
            './/InvoiceDate',
            './/invoicedate',
        ]
        
        for pattern in date_patterns:
            elements = root.xpath(pattern)
            if elements and elements[0].text:
                date_text = elements[0].text.strip()
                parsed_date = self._parse_date(date_text)
                if parsed_date:
                    return parsed_date.strftime(DATE_FORMAT_YYYYMMDD)
        
        return None
    
    def _extract_date_et(self, root) -> Optional[str]:
        """使用ElementTree提取RequestTime日期"""
        date_patterns = [
            'RequestTime',
            'requesttime',
            'REQUESTTIME',
            'InvoiceDate',
            'invoicedate',
        ]
        
        for pattern in date_patterns:
            element = root.find(f'.//{pattern}')
            if element is not None and element.text:
                date_text = element.text.strip()
                parsed_date = self._parse_date(date_text)
                if parsed_date:
                    return parsed_date.strftime(DATE_FORMAT_YYYYMMDD)
        
        return None
    
    def _parse_date(self, date_text: str) -> Optional[datetime]:
        """解析日期字符串"""
        # 常见的日期格式
        date_formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%Y%m%d',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
            '%Y年%m月%d日',
            '%m/%d/%Y',
            '%d/%m/%Y',
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_text, fmt)
            except ValueError:
                continue
        
        return None
    
    def rename_xml_file(self, input_path: str, output_dir: str, folder_name: str = "") -> bool:
        """
        重命名XML文件
        Args:
            input_path: 输入文件路径
            output_dir: 输出目录
            folder_name: 文件夹名称（用作前缀）
        """
        try:
            # 提取XML信息
            seller_name, amount, date = self.extract_xml_info(input_path)
            
            if not all([seller_name, amount, date]):
                print(f"无法提取完整的XML信息: {input_path}")
                print(f"  SellerName: {seller_name}")
                print(f"  金额: {amount}")
                print(f"  日期: {date}")
                return False
            
            # 生成新文件名
            base_filename = XML_FILENAME_FORMAT.format(
                seller_name=seller_name,
                amount=amount,
                date=date
            )

            # 添加文件夹前缀
            if folder_name:
                new_filename = f"{folder_name}+{base_filename}"
            else:
                new_filename = base_filename

            new_filename = sanitize_filename(new_filename) + ".xml"
            
            # 构建输出路径
            output_path = os.path.join(output_dir, new_filename)
            
            # 如果目标文件已存在，添加序号
            counter = 1
            base_output_path = output_path
            while os.path.exists(output_path):
                name, ext = os.path.splitext(base_output_path)
                output_path = f"{name}_{counter}{ext}"
                counter += 1
            
            # 复制文件到新位置
            import shutil
            shutil.copy2(input_path, output_path)
            
            print(f"XML文件重命名成功:")
            print(f"  原文件: {input_path}")
            print(f"  新文件: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"重命名XML文件时出错 {input_path}: {str(e)}")
            return False
