# 发票重命名工具 - 使用说明

## 📦 软件介绍

这是一个可视化的发票重命名工具，可以自动重命名PDF发票文件和XML文件，无需安装Python环境即可运行。

## 🚀 快速开始

### 1. 准备文件
在 `input` 目录中创建以人名命名的子文件夹，例如：
```
input/
├── 张三/
│   ├── invoice1.pdf
│   └── invoice1.xml
├── 李四/
│   ├── invoice2.pdf
│   └── invoice2.xml
└── 王五/
    └── invoice3.xml
```

### 2. 运行程序
双击 `发票重命名工具.exe` 启动程序

### 3. 设置参数
- **输入目录**: 选择包含发票文件的目录（默认为input）
- **输出目录**: 选择重命名后文件的保存位置（默认为output）
- **文件类型**: 选择要处理的文件类型（全部/仅PDF/仅XML）
- **预览模式**: 勾选后只显示重命名结果，不实际重命名文件

### 4. 开始处理
点击"开始处理"按钮，程序会自动：
- 扫描所有子文件夹
- 提取发票信息
- 按规则重命名文件
- 保存到输出目录

## 📋 重命名规则

### PDF文件
- **格式**: `文件夹名+YYYYMM+发票号+发票金额.pdf`
- **示例**: `张三+202403+12345678+1250.00.pdf`

### XML文件
- **格式**: `文件夹名+销售方名称+发票金额+YYYYMMDD.xml`
- **示例**: `张三+北京科技有限公司+1250.00+20240315.xml`

## 🔧 功能特性

- ✅ **可视化界面**: 简单易用的图形界面
- ✅ **批量处理**: 一次性处理多个人员的发票
- ✅ **智能识别**: 自动提取发票信息
- ✅ **预览功能**: 可以先预览结果再执行
- ✅ **进度显示**: 实时显示处理进度
- ✅ **详细日志**: 显示详细的处理过程
- ✅ **错误处理**: 自动跳过无法处理的文件
- ✅ **免安装**: 包含Python环境，无需额外安装

## 📁 目录结构

```
发票重命名工具/
├── 发票重命名工具.exe    # 主程序
├── README.md             # 说明文档
├── input/                # 输入目录
│   ├── 张三/
│   └── 李四/
└── output/               # 输出目录
```

## ⚠️ 注意事项

1. **文件格式**: 仅支持PDF和XML格式的发票文件
2. **文件夹命名**: 子文件夹请使用人名命名
3. **文件安全**: 原始文件不会被修改，重命名后的文件是副本
4. **重复文件**: 如果目标文件名已存在，会自动添加序号
5. **系统要求**: Windows 7及以上版本

## 🆘 常见问题

### Q: 程序无法启动？
A: 请确保：
- Windows系统版本为Win7及以上
- 有足够的磁盘空间
- 杀毒软件没有阻止程序运行

### Q: 无法提取发票信息？
A: 可能原因：
- PDF文件是扫描版，无法提取文本
- XML文件格式不标准
- 发票信息不完整

### Q: 处理速度慢？
A: 这是正常现象，因为需要：
- 解析PDF文件内容
- 提取文本信息
- 识别发票字段

### Q: 如何处理压缩文件？
A: 程序不支持直接处理压缩文件，请先解压后再处理

## 📞 技术支持

如果遇到问题，请检查：
1. 文件格式是否正确
2. 目录权限是否足够
3. 发票文件是否完整

## 🔄 版本信息

- **版本**: v2.0
- **更新日期**: 2024年
- **支持格式**: PDF, XML
- **运行环境**: Windows (免安装)
