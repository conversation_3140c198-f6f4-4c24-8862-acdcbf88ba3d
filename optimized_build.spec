# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 排除不需要的大型模块（保留Python核心模块）
excludes = [
    # 科学计算库
    'matplotlib', 'numpy', 'scipy', 'pandas', 'PIL', 'cv2',
    'torch', 'tensorflow', 'sklearn', 'seaborn', 'plotly',

    # 开发工具
    'jupyter', 'IPython', 'notebook', 'qtconsole', 'spyder',
    'anaconda_navigator', 'conda', 'setuptools', 'pip', 'wheel',
    'distutils', 'pkg_resources',

    # 测试框架（保留基础调试模块）
    'unittest', 'doctest', 'test', 'tests', 'testing',

    # 网络和数据库（保留基础模块）
    'sqlite3', 'ssl', 'http', 'urllib', 'email', 'html',
    'multiprocessing', 'concurrent', 'asyncio', 'socket',
    'socketserver', 'wsgiref', 'xmlrpc',

    # 数据格式（保留基础模块）
    'csv', 'pickle', 'shelve', 'dbm',

    # 压缩（保留基础模块）
    'gzip', 'bz2', 'lzma', 'zipfile', 'tarfile',

    # 音频
    'wave', 'audioop', 'chunk', 'sunau', 'aifc', 'sndhdr',
    'ossaudiodev', 'winsound',

    # 图形界面额外组件
    'turtle', 'tkinter.dnd', 'tkinter.colorchooser',
    'tkinter.commondialog', 'tkinter.simpledialog',
    'tkinter.font', 'tkinter.tix',

    # 其他不需要的模块（保留核心调试模块）
    'curses', 'readline', 'rlcompleter', 'cmd', 'code',
    'codeop', 'py_compile', 'compileall', 'pickletools'
]

a = Analysis(
    ['gui_main.py'],
    pathex=[],
    binaries=[],
    datas=[],  # 移除README.md以减小体积
    hiddenimports=[
        # GUI相关
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',

        # PDF和XML处理
        'PyPDF2',
        'pdfplumber',
        'lxml.etree',
        'xml.etree.ElementTree',

        # Python核心模块
        'dis',
        'inspect',
        'json',
        'threading',
        'queue',
        'pathlib',
        'shutil',
        'os',
        'sys',
        're',
        'datetime',
        'dateutil',
        'dateutil.parser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉不需要的二进制文件
def filter_binaries(binaries):
    filtered = []
    exclude_patterns = [
        'qt', 'matplotlib', 'numpy', 'scipy', 'pandas', 'torch', 
        'tensorflow', 'mkl', 'blas', 'lapack', 'openblas', 'atlas',
        'accelerate', 'libiomp', 'libmmd', 'svml', 'icc', 'mkl_',
        'api-ms-win', 'ucrtbase', 'msvcp', 'vcruntime'
    ]
    
    for binary in binaries:
        name = binary[0].lower()
        if not any(pattern in name for pattern in exclude_patterns):
            filtered.append(binary)
    
    return filtered

# 过滤掉不需要的数据文件
def filter_datas(datas):
    filtered = []
    exclude_patterns = [
        'matplotlib', 'numpy', 'scipy', 'pandas', 'torch', 'tensorflow',
        'test', 'tests', 'example', 'sample', 'demo', 'doc', 'docs',
        'locale', 'translations', 'i18n', 'fonts', 'images', 'icons'
    ]
    
    for data in datas:
        path = data[0].lower()
        if not any(pattern in path for pattern in exclude_patterns):
            filtered.append(data)
    
    return filtered

# 应用过滤器
a.binaries = filter_binaries(a.binaries)
a.datas = filter_datas(a.datas)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='发票重命名工具_优化版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 启用strip以减小体积
    upx=True,    # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
