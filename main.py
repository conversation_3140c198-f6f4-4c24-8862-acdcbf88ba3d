"""
发票重命名工具主程序
"""
import os
import sys
import argparse
from pathlib import Path
from pdf_processor import PDFProcessor
from xml_processor import XMLProcessor
from config import ensure_directories, DEFAULT_INPUT_DIR, DEFAULT_OUTPUT_DIR, SUPPORTED_PDF_EXTENSIONS, SUPPORTED_XML_EXTENSIONS

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='发票文件重命名工具')
    parser.add_argument('--input-dir', '-i', default=DEFAULT_INPUT_DIR, 
                       help=f'输入目录 (默认: {DEFAULT_INPUT_DIR})')
    parser.add_argument('--output-dir', '-o', default=DEFAULT_OUTPUT_DIR,
                       help=f'输出目录 (默认: {DEFAULT_OUTPUT_DIR})')
    parser.add_argument('--file-type', '-t', choices=['pdf', 'xml', 'all'], default='all',
                       help='处理的文件类型 (默认: all)')
    parser.add_argument('--dry-run', action='store_true',
                       help='预览模式，不实际重命名文件')
    
    args = parser.parse_args()
    
    # 确保目录存在
    ensure_directories()
    
    input_dir = args.input_dir
    output_dir = args.output_dir
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在: {input_dir}")
        print(f"请创建目录并放入要处理的文件，或使用 --input-dir 指定其他目录")
        return 1
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化处理器
    pdf_processor = PDFProcessor()
    xml_processor = XMLProcessor()
    
    # 获取文件列表（包含文件夹信息）
    pdf_files = []  # 格式: [(file_path, folder_name), ...]
    xml_files = []  # 格式: [(file_path, folder_name), ...]

    # 遍历输入目录
    input_path = Path(input_dir)

    # 检查是否有子文件夹
    subdirs = [d for d in input_path.iterdir() if d.is_dir()]

    if subdirs:
        # 有子文件夹，遍历每个子文件夹
        print(f"发现 {len(subdirs)} 个子文件夹，开始遍历...")
        for subdir in subdirs:
            folder_name = subdir.name
            print(f"  处理文件夹: {folder_name}")

            for file_path in subdir.rglob('*'):
                if file_path.is_file():
                    ext = file_path.suffix.lower()
                    if ext in SUPPORTED_PDF_EXTENSIONS and args.file_type in ['pdf', 'all']:
                        pdf_files.append((str(file_path), folder_name))
                    elif ext in SUPPORTED_XML_EXTENSIONS and args.file_type in ['xml', 'all']:
                        xml_files.append((str(file_path), folder_name))
    else:
        # 没有子文件夹，直接处理根目录文件
        print("未发现子文件夹，处理根目录文件...")
        for file_path in input_path.iterdir():
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in SUPPORTED_PDF_EXTENSIONS and args.file_type in ['pdf', 'all']:
                    pdf_files.append((str(file_path), ""))
                elif ext in SUPPORTED_XML_EXTENSIONS and args.file_type in ['xml', 'all']:
                    xml_files.append((str(file_path), ""))
    
    print(f"找到 {len(pdf_files)} 个PDF文件和 {len(xml_files)} 个XML文件")
    
    if not pdf_files and not xml_files:
        print("没有找到要处理的文件")
        return 0
    
    # 处理统计
    success_count = 0
    total_count = len(pdf_files) + len(xml_files)
    
    # 处理PDF文件
    if pdf_files:
        print(f"\n开始处理 {len(pdf_files)} 个PDF文件...")
        for i, (pdf_file, folder_name) in enumerate(pdf_files, 1):
            folder_info = f" (来自文件夹: {folder_name})" if folder_name else ""
            print(f"\n[{i}/{len(pdf_files)}] 处理PDF文件: {pdf_file}{folder_info}")

            if args.dry_run:
                # 预览模式，只提取信息不重命名
                year_month, invoice_number, amount = pdf_processor.extract_invoice_info(pdf_file)
                if all([year_month, invoice_number, amount]):
                    prefix = f"{folder_name}+" if folder_name else ""
                    new_name = f"{prefix}{year_month}+{invoice_number}+{amount}.pdf"
                    print(f"  预览新文件名: {new_name}")
                    success_count += 1
                else:
                    print(f"  无法提取完整信息")
            else:
                if pdf_processor.rename_pdf_file(pdf_file, output_dir, folder_name):
                    success_count += 1
    
    # 处理XML文件
    if xml_files:
        print(f"\n开始处理 {len(xml_files)} 个XML文件...")
        for i, (xml_file, folder_name) in enumerate(xml_files, 1):
            folder_info = f" (来自文件夹: {folder_name})" if folder_name else ""
            print(f"\n[{i}/{len(xml_files)}] 处理XML文件: {xml_file}{folder_info}")

            if args.dry_run:
                # 预览模式，只提取信息不重命名
                seller_name, amount, date = xml_processor.extract_xml_info(xml_file)
                if all([seller_name, amount, date]):
                    prefix = f"{folder_name}+" if folder_name else ""
                    new_name = f"{prefix}{seller_name}+{amount}+{date}.xml"
                    print(f"  预览新文件名: {new_name}")
                    success_count += 1
                else:
                    print(f"  无法提取完整信息")
            else:
                if xml_processor.rename_xml_file(xml_file, output_dir, folder_name):
                    success_count += 1
    
    # 输出结果统计
    print(f"\n处理完成!")
    print(f"总文件数: {total_count}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    
    if not args.dry_run and success_count > 0:
        print(f"重命名后的文件保存在: {output_dir}")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        sys.exit(1)
