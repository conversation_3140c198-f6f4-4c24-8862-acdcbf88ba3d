"""
PDF发票处理模块
"""
import os
import re
from datetime import datetime
from typing import Optional, Tuple
import PyPDF2
import pdfplumber
from utils import sanitize_filename, extract_amount_from_text, extract_date_from_text
from config import PDF_FILENAME_FORMAT, DATE_FORMAT_YYYYMM

class PDFProcessor:
    def __init__(self):
        self.supported_extensions = ['.pdf']
    
    def extract_invoice_info(self, pdf_path: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        从PDF中提取发票信息
        返回: (年月, 发票号, 金额)
        """
        try:
            # 首先尝试使用pdfplumber提取文本
            text = self._extract_text_with_pdfplumber(pdf_path)
            if not text:
                # 如果pdfplumber失败，尝试PyPDF2
                text = self._extract_text_with_pypdf2(pdf_path)
            
            if not text:
                print(f"无法从PDF文件提取文本: {pdf_path}")
                return None, None, None
            
            # 提取发票信息
            year_month = self._extract_year_month(text)
            invoice_number = self._extract_invoice_number(text)
            amount = self._extract_amount(text)
            
            return year_month, invoice_number, amount
            
        except Exception as e:
            print(f"处理PDF文件时出错 {pdf_path}: {str(e)}")
            return None, None, None
    
    def _extract_text_with_pdfplumber(self, pdf_path: str) -> str:
        """使用pdfplumber提取PDF文本"""
        try:
            with pdfplumber.open(pdf_path) as pdf:
                text = ""
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                return text
        except Exception as e:
            print(f"pdfplumber提取文本失败: {str(e)}")
            return ""
    
    def _extract_text_with_pypdf2(self, pdf_path: str) -> str:
        """使用PyPDF2提取PDF文本"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"PyPDF2提取文本失败: {str(e)}")
            return ""
    
    def _extract_year_month(self, text: str) -> Optional[str]:
        """从文本中提取年月信息"""
        # 查找日期模式
        date_obj = extract_date_from_text(text)
        if date_obj:
            return date_obj.strftime(DATE_FORMAT_YYYYMM)
        
        # 如果没有找到完整日期，尝试查找年月模式
        year_month_patterns = [
            r'(\d{4})年(\d{1,2})月',
            r'(\d{4})-(\d{1,2})',
            r'(\d{4})/(\d{1,2})',
            r'(\d{6})',  # YYYYMM格式
        ]
        
        for pattern in year_month_patterns:
            matches = re.findall(pattern, text)
            if matches:
                if len(matches[0]) == 2:  # 年和月分开
                    year, month = matches[0]
                    return f"{year}{month.zfill(2)}"
                else:  # YYYYMM格式
                    return matches[0]
        
        return None
    
    def _extract_invoice_number(self, text: str) -> Optional[str]:
        """从文本中提取发票号"""
        # 发票号的常见模式
        invoice_patterns = [
            r'发票号码[：:]\s*([A-Z0-9]+)',
            r'发票代码[：:]\s*([A-Z0-9]+)',
            r'Invoice\s*No[.:]?\s*([A-Z0-9]+)',
            r'发票号[：:]\s*([A-Z0-9]+)',
            r'票据号[：:]\s*([A-Z0-9]+)',
            r'([0-9]{8,})',  # 8位以上数字
        ]
        
        for pattern in invoice_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return matches[0]
        
        return None
    
    def _extract_amount(self, text: str) -> Optional[str]:
        """从文本中提取金额"""
        # 查找总金额相关的模式
        amount_patterns = [
            r'合计金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'总金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'应付金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'Total[：:]?\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'Amount[：:]?\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        ]
        
        for pattern in amount_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                amount = matches[0].replace(',', '')
                return amount
        
        # 如果没有找到特定标签的金额，使用通用金额提取
        return extract_amount_from_text(text)
    
    def rename_pdf_file(self, input_path: str, output_dir: str, folder_name: str = "") -> bool:
        """
        重命名PDF文件
        Args:
            input_path: 输入文件路径
            output_dir: 输出目录
            folder_name: 文件夹名称（用作前缀）
        """
        try:
            # 提取发票信息
            year_month, invoice_number, amount = self.extract_invoice_info(input_path)
            
            if not all([year_month, invoice_number, amount]):
                print(f"无法提取完整的发票信息: {input_path}")
                print(f"  年月: {year_month}")
                print(f"  发票号: {invoice_number}")
                print(f"  金额: {amount}")
                return False
            
            # 生成新文件名
            base_filename = PDF_FILENAME_FORMAT.format(
                year_month=year_month,
                invoice_number=invoice_number,
                amount=amount
            )

            # 添加文件夹前缀
            if folder_name:
                new_filename = f"{folder_name}+{base_filename}"
            else:
                new_filename = base_filename

            new_filename = sanitize_filename(new_filename) + ".pdf"
            
            # 构建输出路径
            output_path = os.path.join(output_dir, new_filename)
            
            # 如果目标文件已存在，添加序号
            counter = 1
            base_output_path = output_path
            while os.path.exists(output_path):
                name, ext = os.path.splitext(base_output_path)
                output_path = f"{name}_{counter}{ext}"
                counter += 1
            
            # 复制文件到新位置
            import shutil
            shutil.copy2(input_path, output_path)
            
            print(f"PDF文件重命名成功:")
            print(f"  原文件: {input_path}")
            print(f"  新文件: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"重命名PDF文件时出错 {input_path}: {str(e)}")
            return False
