(['D:\\utils\\RenameReceipt\\gui_main.py'],
 ['D:\\utils\\RenameReceipt'],
 ['tkinter',
  'tkinter.ttk',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'tkinter.scrolledtext',
  'PyPDF2',
  'pdfplumber',
  'lxml.etree',
  'xml.etree.ElementTree',
  'threading',
  'queue',
  'pathlib',
  'shutil',
  'dateutil.parser'],
 [('D:\\Program Files\\anaconda3\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Program Files\\anaconda3\\Lib\\site-packages\\ormsgpack\\_pyinstaller',
   0),
  ('D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\randomname\\__pyinstaller',
   0),
  ('D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib',
  'numpy',
  'scipy',
  'pandas',
  'PIL',
  'cv2',
  'torch',
  'tensorflow',
  'sklearn',
  'seaborn',
  'plotly',
  'jupyter',
  'IPython',
  'notebook',
  'qtconsole',
  'spyder',
  'anaconda_navigator',
  'conda',
  'unittest',
  'doctest',
  'test',
  'tests',
  'testing',
  'multiprocessing',
  'concurrent',
  'asyncio',
  'wave',
  'audioop',
  'chunk',
  'sunau',
  'aifc',
  'sndhdr',
  'ossaudiodev',
  'winsound',
  'turtle',
  'tkinter.dnd',
  'tkinter.colorchooser',
  'tkinter.commondialog',
  'tkinter.simpledialog',
  'tkinter.font',
  'tkinter.tix',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.3 | packaged by Anaconda, Inc. | (main, Apr 19 2023, 23:46:34) [MSC '
 'v.1916 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('gui_main', 'D:\\utils\\RenameReceipt\\gui_main.py', 'PYSOURCE')],
 [('pkg_resources',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('typing', 'D:\\Program Files\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib',
   'D:\\Program Files\\anaconda3\\Lib\\contextlib.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Program Files\\anaconda3\\Lib\\__future__.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Program Files\\anaconda3\\Lib\\subprocess.py',
   'PYMODULE'),
  ('selectors', 'D:\\Program Files\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('struct', 'D:\\Program Files\\anaconda3\\Lib\\struct.py', 'PYMODULE'),
  ('logging',
   'D:\\Program Files\\anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Program Files\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files\\anaconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files\\anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'D:\\Program Files\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files\\anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email',
   'D:\\Program Files\\anaconda3\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('urllib',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files\\anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'D:\\Program Files\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files\\anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files\\anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\Program Files\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\Program Files\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('datetime', 'D:\\Program Files\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\Program Files\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('random', 'D:\\Program Files\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Program Files\\anaconda3\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Program Files\\anaconda3\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Program Files\\anaconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions', 'D:\\Program Files\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Program Files\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Program Files\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\Program Files\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files\\anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files\\anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'D:\\Program Files\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files\\anaconda3\\Lib\\configparser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\Program Files\\anaconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files\\anaconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Program Files\\anaconda3\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Program Files\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Program Files\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp', 'D:\\Program Files\\anaconda3\\Lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\Program Files\\anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('textwrap', 'D:\\Program Files\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'D:\\Program Files\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('email.parser',
   'D:\\Program Files\\anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Program Files\\anaconda3\\Lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Program Files\\anaconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Program Files\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\Program Files\\anaconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Program Files\\anaconda3\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Program Files\\anaconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Program Files\\anaconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\Program Files\\anaconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Program Files\\anaconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files\\anaconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\Program Files\\anaconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Program Files\\anaconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files\\anaconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Program Files\\anaconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform', 'D:\\Program Files\\anaconda3\\Lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Program Files\\anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Program Files\\anaconda3\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipfile', 'D:\\Program Files\\anaconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Program Files\\anaconda3\\Lib\\py_compile.py',
   'PYMODULE'),
  ('lzma', 'D:\\Program Files\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\Program Files\\anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\Program Files\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Program Files\\anaconda3\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Program Files\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Program Files\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('win32con',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'D:\\Program Files\\anaconda3\\Lib\\cgi.py', 'PYMODULE'),
  ('html', 'D:\\Program Files\\anaconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Program Files\\anaconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob', 'D:\\Program Files\\anaconda3\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'D:\\Program Files\\anaconda3\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files\\anaconda3\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('readline',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files\\anaconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Program Files\\anaconda3\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('smtplib', 'D:\\Program Files\\anaconda3\\Lib\\smtplib.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files\\anaconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files\\anaconda3\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files\\anaconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Program Files\\anaconda3\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Program Files\\anaconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Program Files\\anaconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files\\anaconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files\\anaconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files\\anaconda3\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('json', 'D:\\Program Files\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files\\anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files\\anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files\\anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('zipp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Program Files\\anaconda3\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('six',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('shutil', 'D:\\Program Files\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('bs4',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('chardet',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('html5lib.treebuilders.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('html5lib.treebuilders',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree_lxml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib._ihatexml',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\Program Files\\anaconda3\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('html5lib.treebuilders.dom',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('html5lib._utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_utils.py',
   'PYMODULE'),
  ('html5lib.constants',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\constants.py',
   'PYMODULE'),
  ('html5lib',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\__init__.py',
   'PYMODULE'),
  ('html5lib.serializer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\serializer.py',
   'PYMODULE'),
  ('html5lib.filters.optionaltags',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('html5lib.filters.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('html5lib.filters',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('html5lib.filters.sanitizer',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('html5lib.filters.whitespace',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('html5lib.filters.alphabeticalattributes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('html5lib.filters.inject_meta_charset',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('html5lib.treewalkers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree_lxml',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers.genshi',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('html5lib.treewalkers.dom',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('html5lib.treewalkers.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('html5lib.html5parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('html5lib._tokenizer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('html5lib._trie',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('html5lib._trie.py',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('html5lib._trie._base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('html5lib._inputstream',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('webencodings',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\webencodings\\__init__.py',
   'PYMODULE'),
  ('webencodings.x_user_defined',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('webencodings.labels',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\webencodings\\labels.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser',
   'D:\\Program Files\\anaconda3\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'D:\\Program Files\\anaconda3\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('difflib', 'D:\\Program Files\\anaconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('lxml.html.defs',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('optparse', 'D:\\Program Files\\anaconda3\\Lib\\optparse.py', 'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('cssselect',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('pdfplumber',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.repair',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\repair.py',
   'PYMODULE'),
  ('pdfplumber.pdf',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\pdf.py',
   'PYMODULE'),
  ('pdfplumber.page',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\page.py',
   'PYMODULE'),
  ('pdfplumber.display',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\display.py',
   'PYMODULE'),
  ('pypdfium2',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\utils.py',
   'PYMODULE'),
  ('pypdfium2.internal.consts',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\consts.py',
   'PYMODULE'),
  ('pypdfium2.internal.bases',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\internal\\bases.py',
   'PYMODULE'),
  ('uuid', 'D:\\Program Files\\anaconda3\\Lib\\uuid.py', 'PYMODULE'),
  ('pypdfium2.raw',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\raw.py',
   'PYMODULE'),
  ('pypdfium2_raw.bindings',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2_raw\\bindings.py',
   'PYMODULE'),
  ('pypdfium2_raw',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2_raw\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers.textpage',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\textpage.py',
   'PYMODULE'),
  ('pypdfium2._helpers.pageobjects',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\pageobjects.py',
   'PYMODULE'),
  ('pypdfium2._helpers.page',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\page.py',
   'PYMODULE'),
  ('pypdfium2._helpers.attachment',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\attachment.py',
   'PYMODULE'),
  ('pypdfium2._helpers.document',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\document.py',
   'PYMODULE'),
  ('pypdfium2._helpers.bitmap',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\bitmap.py',
   'PYMODULE'),
  ('pypdfium2._helpers.matrix',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\matrix.py',
   'PYMODULE'),
  ('pypdfium2._helpers.misc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\misc.py',
   'PYMODULE'),
  ('pypdfium2._helpers.unsupported',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_helpers\\unsupported.py',
   'PYMODULE'),
  ('pypdfium2.version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\version.py',
   'PYMODULE'),
  ('pypdfium2._library_scope',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\_library_scope.py',
   'PYMODULE'),
  ('pdfplumber.utils.text',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\text.py',
   'PYMODULE'),
  ('pdfplumber.utils.geometry',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\geometry.py',
   'PYMODULE'),
  ('pdfplumber.utils.generic',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\generic.py',
   'PYMODULE'),
  ('pdfplumber.utils.clustering',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\clustering.py',
   'PYMODULE'),
  ('pdfplumber.table',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\table.py',
   'PYMODULE'),
  ('pdfminer.converter',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\converter.py',
   'PYMODULE'),
  ('pdfminer.pdffont',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdffont.py',
   'PYMODULE'),
  ('pdfminer.fontmetrics',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\fontmetrics.py',
   'PYMODULE'),
  ('pdfminer.encodingdb',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\encodingdb.py',
   'PYMODULE'),
  ('pdfminer.latin_enc',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\latin_enc.py',
   'PYMODULE'),
  ('pdfminer.glyphlist',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\glyphlist.py',
   'PYMODULE'),
  ('pdfminer.cmapdb',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmapdb.py',
   'PYMODULE'),
  ('pdfminer.settings',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\settings.py',
   'PYMODULE'),
  ('pdfminer.pdfdevice',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfdevice.py',
   'PYMODULE'),
  ('pdfminer.image',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\image.py',
   'PYMODULE'),
  ('pdfminer.jbig2',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\jbig2.py',
   'PYMODULE'),
  ('pdfminer.utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\utils.py',
   'PYMODULE'),
  ('pdfminer.pdfcolor',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfcolor.py',
   'PYMODULE'),
  ('pdfplumber.container',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\container.py',
   'PYMODULE'),
  ('pdfplumber.convert',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\convert.py',
   'PYMODULE'),
  ('pdfplumber._typing',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\_typing.py',
   'PYMODULE'),
  ('pdfminer.psparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\psparser.py',
   'PYMODULE'),
  ('pdfminer.pdfparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfparser.py',
   'PYMODULE'),
  ('pdfminer.pdfpage',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfpage.py',
   'PYMODULE'),
  ('pdfminer.pdfinterp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfinterp.py',
   'PYMODULE'),
  ('pdfminer.pdfdocument',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdfdocument.py',
   'PYMODULE'),
  ('pdfminer._saslprep',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\_saslprep.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\Program Files\\anaconda3\\Lib\\stringprep.py',
   'PYMODULE'),
  ('pdfminer.data_structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\data_structures.py',
   'PYMODULE'),
  ('pdfminer.arcfour',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\arcfour.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\Program Files\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('pdfminer.layout',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\layout.py',
   'PYMODULE'),
  ('pdfplumber._version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\_version.py',
   'PYMODULE'),
  ('pdfplumber.utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.utils.pdfinternals',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfplumber\\utils\\pdfinternals.py',
   'PYMODULE'),
  ('pdfminer.pdftypes',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\pdftypes.py',
   'PYMODULE'),
  ('pdfminer.runlength',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\runlength.py',
   'PYMODULE'),
  ('pdfminer.lzw',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\lzw.py',
   'PYMODULE'),
  ('pdfminer.ccitt',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\ccitt.py',
   'PYMODULE'),
  ('pdfminer.ascii85',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\ascii85.py',
   'PYMODULE'),
  ('pdfminer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\__init__.py',
   'PYMODULE'),
  ('PyPDF2',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PyPDF2.xmp',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\xmp.py',
   'PYMODULE'),
  ('PyPDF2.generic._utils',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._cmap',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('cffi',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC4',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\ARC4.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Random',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files\\anaconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('config', 'D:\\utils\\RenameReceipt\\config.py', 'PYMODULE'),
  ('xml_processor', 'D:\\utils\\RenameReceipt\\xml_processor.py', 'PYMODULE'),
  ('utils', 'D:\\utils\\RenameReceipt\\utils.py', 'PYMODULE'),
  ('pdf_processor', 'D:\\utils\\RenameReceipt\\pdf_processor.py', 'PYMODULE'),
  ('queue', 'D:\\Program Files\\anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('pathlib', 'D:\\Program Files\\anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('threading', 'D:\\Program Files\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Program Files\\anaconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE')],
 [('pypdfium2_raw\\pdfium.dll',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2_raw\\pdfium.dll',
   'BINARY'),
  ('python311.dll', 'D:\\Program Files\\anaconda3\\python311.dll', 'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('select.pyd', 'D:\\Program Files\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Program Files\\anaconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\Program Files\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Program Files\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\clean.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\html\\clean.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Program Files\\anaconda3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_openssl.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\Program Files\\anaconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Program Files\\anaconda3\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files\\anaconda3\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\Program Files\\anaconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll', 'C:\\GDAL\\libcrypto-1_1-x64.dll', 'BINARY'),
  ('ffi.dll', 'D:\\Program Files\\anaconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('libssl-1_1-x64.dll', 'C:\\GDAL\\libssl-1_1-x64.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\Program Files\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\Program Files\\anaconda3\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files\\anaconda3\\python3.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('tk86t.dll',
   'D:\\Program Files\\anaconda3\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'D:\\Program Files\\anaconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Program Files\\anaconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Program '
   'Files\\anaconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Program Files\\anaconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('pypdfium2\\version.json',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pypdfium2\\version.json',
   'DATA'),
  ('pypdfium2_raw\\version.json',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pypdfium2_raw\\version.json',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\LICENSE.PSF',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\LICENSE.PSF',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\LICENSE.BSD',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\WHEEL',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\direct_url.json',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\direct_url.json',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\METADATA',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\METADATA',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\top_level.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\LICENSE',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\LICENSE.APACHE',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\INSTALLER',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\REQUESTED',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-39.0.1.dist-info\\RECORD',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\cryptography-39.0.1.dist-info\\RECORD',
   'DATA'),
  ('pdfminer\\cmap\\ETen-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\ETen-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\CNS-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Add-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\83pv-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\83pv-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETHK-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\ETHK-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\NWP-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\NWP-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UCS2-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GB-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF8-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm314-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKm314-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\NWP-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\NWP-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF8-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCpc-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSCpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GB-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\WP-Symbol-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\WP-Symbol-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF16-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCpc-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSCpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSC-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF8-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBT-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-CNS1.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-CNS1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hiragana-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Hiragana-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-HW-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-HW-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdla-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKdla-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-Korea1.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-Korea1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS1-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\CNS1-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\83pv-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\83pv-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF8-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETenms-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\ETenms-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Roman-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Roman-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK2K-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBK2K-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90ms-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\90ms-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Ext-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKgccs-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKgccs-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBT-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKscs-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKscs-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-HW-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-HW-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX0213-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX0213-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBKp-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBKp-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF16-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBTpc-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBTpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF16-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-Johab-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSC-Johab-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBKp-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBKp-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX0213-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX0213-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdlb-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKdlb-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBpc-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETenms-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\ETenms-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Ext-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX02132004-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX02132004-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBpc-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Ext-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Ext-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GB-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF16-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-GB1.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-GB1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF8-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBK-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Katakana-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Katakana-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hiragana-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Hiragana-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF8-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF8-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90pv-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\90pv-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF16-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90pv-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\90pv-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-HW-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-HW-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UCS2-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF16-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdlb-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKdlb-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKscs-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKscs-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm314-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKm314-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX02132004-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX02132004-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GB-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-HW-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-HW-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Add-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF32-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS2-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\CNS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm471-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKm471-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Roman-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Roman-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKgccs-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKgccs-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdla-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKdla-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UCS2-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78ms-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78ms-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF32-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90msp-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\90msp-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF16-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF16-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UCS2-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Katakana-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Katakana-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UCS2-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSC-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\CNS-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Add-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF8-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBK-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF16-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF8-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-Johab-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\KSC-Johab-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90msp-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\90msp-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF8-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm471-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\HKm471-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hankaku-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Hankaku-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK2K-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBK2K-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UCS2-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBT-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-Japan1.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-Japan1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hankaku-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Hankaku-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90ms-RKSJ-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\90ms-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETHK-B5-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\ETHK-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\WP-Symbol-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\WP-Symbol-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5pc-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\B5pc-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS2-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\CNS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78ms-RKSJ-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\78ms-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETen-B5-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\ETen-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS1-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\CNS1-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-EUC-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBT-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\py.typed',
   'D:\\Program Files\\anaconda3\\Lib\\site-packages\\pdfminer\\py.typed',
   'DATA'),
  ('pdfminer\\cmap\\GBTpc-EUC-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\GBTpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF16-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5pc-V.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\B5pc-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-H.pickle.gz',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pdfminer\\cmap\\Add-H.pickle.gz',
   'DATA'),
  ('importlib_metadata-8.4.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata-8.4.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.4.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata-8.4.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.4.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata-8.4.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.4.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata-8.4.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.4.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata-8.4.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.4.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\importlib_metadata-8.4.0.dist-info\\WHEEL',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:/Program '
   'Files/anaconda3/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:/Program Files/anaconda3/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:/Program Files/anaconda3/Library/lib\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl8\\8.6\\tdbc\\sqlite3-1.1.3.tm',
   'D:/Program Files/anaconda3/Library/lib\\tcl8\\8.6\\tdbc\\sqlite3-1.1.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:/Program Files/anaconda3/Library/lib\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:/Program '
   'Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:/Program Files/anaconda3/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:/Program Files/anaconda3/Library/lib\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:/Program Files/anaconda3/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'D:\\Program '
   'Files\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'DATA'),
  ('base_library.zip',
   'D:\\utils\\RenameReceipt\\build\\fixed_build\\base_library.zip',
   'DATA')],
 [('traceback', 'D:\\Program Files\\anaconda3\\Lib\\traceback.py', 'PYMODULE'),
  ('posixpath', 'D:\\Program Files\\anaconda3\\Lib\\posixpath.py', 'PYMODULE'),
  ('abc', 'D:\\Program Files\\anaconda3\\Lib\\abc.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\Program Files\\anaconda3\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\Program Files\\anaconda3\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\Program Files\\anaconda3\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Program Files\\anaconda3\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('keyword', 'D:\\Program Files\\anaconda3\\Lib\\keyword.py', 'PYMODULE'),
  ('re._parser',
   'D:\\Program Files\\anaconda3\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\Program Files\\anaconda3\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\Program Files\\anaconda3\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\Program Files\\anaconda3\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'D:\\Program Files\\anaconda3\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Program Files\\anaconda3\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('genericpath',
   'D:\\Program Files\\anaconda3\\Lib\\genericpath.py',
   'PYMODULE'),
  ('functools', 'D:\\Program Files\\anaconda3\\Lib\\functools.py', 'PYMODULE'),
  ('weakref', 'D:\\Program Files\\anaconda3\\Lib\\weakref.py', 'PYMODULE'),
  ('operator', 'D:\\Program Files\\anaconda3\\Lib\\operator.py', 'PYMODULE'),
  ('reprlib', 'D:\\Program Files\\anaconda3\\Lib\\reprlib.py', 'PYMODULE'),
  ('codecs', 'D:\\Program Files\\anaconda3\\Lib\\codecs.py', 'PYMODULE'),
  ('locale', 'D:\\Program Files\\anaconda3\\Lib\\locale.py', 'PYMODULE'),
  ('stat', 'D:\\Program Files\\anaconda3\\Lib\\stat.py', 'PYMODULE'),
  ('io', 'D:\\Program Files\\anaconda3\\Lib\\io.py', 'PYMODULE'),
  ('copyreg', 'D:\\Program Files\\anaconda3\\Lib\\copyreg.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Program Files\\anaconda3\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('types', 'D:\\Program Files\\anaconda3\\Lib\\types.py', 'PYMODULE'),
  ('warnings', 'D:\\Program Files\\anaconda3\\Lib\\warnings.py', 'PYMODULE'),
  ('enum', 'D:\\Program Files\\anaconda3\\Lib\\enum.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Program Files\\anaconda3\\Lib\\sre_parse.py', 'PYMODULE'),
  ('heapq', 'D:\\Program Files\\anaconda3\\Lib\\heapq.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\Program Files\\anaconda3\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('linecache', 'D:\\Program Files\\anaconda3\\Lib\\linecache.py', 'PYMODULE'),
  ('ntpath', 'D:\\Program Files\\anaconda3\\Lib\\ntpath.py', 'PYMODULE'),
  ('os', 'D:\\Program Files\\anaconda3\\Lib\\os.py', 'PYMODULE')])
