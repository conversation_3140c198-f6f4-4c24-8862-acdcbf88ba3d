"""
配置文件
"""
import os

# 默认处理目录
DEFAULT_INPUT_DIR = "input"
DEFAULT_OUTPUT_DIR = "output"

# 支持的文件格式
SUPPORTED_PDF_EXTENSIONS = ['.pdf']
SUPPORTED_XML_EXTENSIONS = ['.xml']

# 文件名格式配置
PDF_FILENAME_FORMAT = "{year_month}+{invoice_number}+{amount}"
XML_FILENAME_FORMAT = "{seller_name}+{amount}+{date}"

# 日期格式
DATE_FORMAT_YYYYMM = "%Y%m"
DATE_FORMAT_YYYYMMDD = "%Y%m%d"

# 创建目录（如果不存在）
def ensure_directories():
    """确保输入和输出目录存在"""
    os.makedirs(DEFAULT_INPUT_DIR, exist_ok=True)
    os.makedirs(DEFAULT_OUTPUT_DIR, exist_ok=True)
