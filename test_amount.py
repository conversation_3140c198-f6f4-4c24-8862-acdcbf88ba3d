#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def test_amount_extraction():
    # 测试文本（从之前的调试输出中获取）
    text = """电子发票（普通发票）
发票号码：25927000000138440246
开票日期：2025年09月05日
下载次数：1
购 名称：青岛市勘察测绘研究院 销 名称：青岛市琴岛通卡股份有限公司
买 售
方 方
信 统一社会信用代码/纳税人识别号：913702001635757573 信 统一社会信用代码/纳税人识别号：91370200693791887J
息 息
项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额
*预付卡销售*充值款 100.00 不征税
*预付卡销售*充值款 100.00 不征税
*预付卡销售*充值款 100.00 不征税
*预付卡销售*充值款 100.00 不征税
合 计 ¥400.00 ¥0.00
价税合计（大写） 肆佰圆整 （小写）¥400.00
2660001610135336
备
注
开票人：于翠莲"""

    print("测试金额提取...")
    
    # 新的金额提取模式
    amount_patterns = [
        # 价税合计相关
        r'价税合计[^¥]*¥(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'价税合计.*?（小写）[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'价税合计.*?[¥$](\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        
        # 合计相关
        r'合\s*计[^¥]*¥(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'合计金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'总金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'应付金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        
        # 英文模式
        r'Total[：:]?\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'Amount[：:]?\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        
        # 通用模式：查找¥符号后的金额
        r'¥(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
    ]
    
    for i, pattern in enumerate(amount_patterns):
        matches = re.findall(pattern, text, re.IGNORECASE)
        print(f'模式 {i+1}: {pattern}')
        print(f'  匹配结果: {matches}')
        if matches:
            amount = matches[-1].replace(',', '')
            try:
                amount_float = float(amount)
                if 0 < amount_float < 1000000:
                    print(f'  ✓ 找到有效金额: {amount}')
                    return amount
            except ValueError:
                continue
    
    print("没有找到有效金额")
    return None

if __name__ == "__main__":
    result = test_amount_extraction()
    print(f"\n最终结果: {result}")
