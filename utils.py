"""
通用工具函数
"""
import os
import re
from datetime import datetime
from typing import Optional

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除或替换不合法的字符
    """
    # 移除或替换不合法的文件名字符
    illegal_chars = r'[<>:"/\\|?*]'
    filename = re.sub(illegal_chars, '_', filename)
    
    # 移除多余的空格和点
    filename = re.sub(r'\s+', ' ', filename).strip()
    filename = filename.strip('.')
    
    # 限制文件名长度
    if len(filename) > 200:
        filename = filename[:200]
    
    return filename

def extract_amount_from_text(text: str) -> Optional[str]:
    """
    从文本中提取金额
    支持多种金额格式：123.45, 1,234.56, ¥123.45, $123.45等
    """
    # 匹配金额的正则表达式
    amount_patterns = [
        r'[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # 带千分位分隔符
        r'[¥$]?(\d+\.\d{2})',  # 简单小数格式
        r'[¥$]?(\d+)',  # 整数格式
    ]
    
    for pattern in amount_patterns:
        matches = re.findall(pattern, text)
        if matches:
            # 返回最大的金额（通常是总金额）
            amounts = [float(match.replace(',', '')) for match in matches]
            return str(max(amounts))
    
    return None

def extract_date_from_text(text: str) -> Optional[datetime]:
    """
    从文本中提取日期
    支持多种日期格式
    """
    date_patterns = [
        r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})',  # YYYY-MM-DD 或 YYYY/MM/DD
        r'(\d{1,2}[-/]\d{1,2}[-/]\d{4})',  # MM-DD-YYYY 或 MM/DD/YYYY
        r'(\d{4}年\d{1,2}月\d{1,2}日)',     # 中文日期格式
    ]
    
    for pattern in date_patterns:
        matches = re.findall(pattern, text)
        if matches:
            date_str = matches[0]
            try:
                # 尝试不同的日期格式解析
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%m-%d-%Y', '%m/%d/%Y', '%Y年%m月%d日']:
                    try:
                        return datetime.strptime(date_str, fmt)
                    except ValueError:
                        continue
            except:
                continue
    
    return None

def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return os.path.splitext(filename)[1].lower()

def create_backup_name(original_path: str) -> str:
    """创建备份文件名"""
    dir_name = os.path.dirname(original_path)
    base_name = os.path.basename(original_path)
    name, ext = os.path.splitext(base_name)
    
    counter = 1
    while True:
        backup_name = f"{name}_backup_{counter}{ext}"
        backup_path = os.path.join(dir_name, backup_name)
        if not os.path.exists(backup_path):
            return backup_path
        counter += 1
