"""
测试PDF处理的调试脚本
"""
from pdf_processor import PDFProcessor
import os

def test_pdf_processing():
    processor = PDFProcessor()
    
    # 查找一个PDF文件进行测试
    pdf_files = []
    for root, dirs, files in os.walk('input'):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    if not pdf_files:
        print("没有找到PDF文件")
        return
    
    # 测试第一个PDF文件
    pdf_path = pdf_files[0]
    print(f'测试文件: {pdf_path}')
    
    # 提取文本
    text1 = processor._extract_text_with_pdfplumber(pdf_path)
    text2 = processor._extract_text_with_pypdf2(pdf_path)
    
    print(f'pdfplumber提取的文本长度: {len(text1)}')
    print(f'PyPDF2提取的文本长度: {len(text2)}')
    
    # 选择更好的文本
    text = text1 if text1 else text2
    
    if text:
        print('\n=== 提取的文本内容 ===')
        print(text[:1000])  # 显示前1000个字符
        print('\n=== 文本内容结束 ===\n')
        
        # 测试各个提取函数
        year_month = processor._extract_year_month(text)
        invoice_number = processor._extract_invoice_number(text)
        amount = processor._extract_amount(text)
        
        print(f'提取结果:')
        print(f'  年月: {year_month}')
        print(f'  发票号: {invoice_number}')
        print(f'  金额: {amount}')
        
        # 测试金额提取的详细过程
        print('\n=== 金额提取调试 ===')
        import re
        
        # 测试各种金额模式
        amount_patterns = [
            r'合计金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'总金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'应付金额[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'价税合计[：:]\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'Total[：:]?\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'Amount[：:]?\s*[¥$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        ]
        
        for i, pattern in enumerate(amount_patterns):
            matches = re.findall(pattern, text, re.IGNORECASE)
            print(f'模式 {i+1}: {pattern}')
            print(f'  匹配结果: {matches}')
        
        # 查找所有数字
        all_numbers = re.findall(r'\d+\.?\d*', text)
        print(f'\n所有数字: {all_numbers[:20]}')  # 显示前20个数字
        
    else:
        print("无法提取文本内容")

if __name__ == "__main__":
    test_pdf_processing()
