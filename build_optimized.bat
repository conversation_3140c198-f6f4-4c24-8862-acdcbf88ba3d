@echo off
chcp 65001 >nul
echo ========================================
echo 发票重命名工具 - 优化打包脚本
echo ========================================
echo.

echo 1. 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del /q "*.spec"

echo 2. 检查依赖...
python -c "import tkinter, PyPDF2, pdfplumber, lxml" 2>nul
if errorlevel 1 (
    echo 错误：缺少必要的依赖包
    echo 正在安装最小依赖...
    pip install PyPDF2==3.0.1 pdfplumber==0.10.0 lxml==4.9.3
)

echo 3. 开始优化打包...
echo 正在使用优化配置构建可执行文件...
pyinstaller --clean --noconfirm optimized_build.spec

echo.
if exist "dist\发票重命名工具_优化版.exe" (
    echo ✓ 打包成功！
    echo.
    echo 文件位置: dist\发票重命名工具_优化版.exe
    
    REM 获取文件大小
    for %%A in ("dist\发票重命名工具_优化版.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
        echo 文件大小: !sizeMB! MB
    )
    
    echo.
    echo 4. 清理临时文件...
    if exist "build" rmdir /s /q "build"
    
    echo.
    echo ========================================
    echo 打包完成！可执行文件已生成。
    echo ========================================
) else (
    echo ✗ 打包失败！
    echo 请检查错误信息。
)

echo.
pause
