@echo off
chcp 65001 >nul
echo ========================================
echo 发票重命名工具 - 打包脚本
echo ========================================
echo.

echo 1. 安装依赖包...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

echo.
echo 2. 清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo.
echo 3. 开始打包...
pyinstaller --onefile --windowed --name "发票重命名工具" ^
    --add-data "README.md;." ^
    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import tkinter.filedialog ^
    --hidden-import tkinter.messagebox ^
    --hidden-import tkinter.scrolledtext ^
    --hidden-import PyPDF2 ^
    --hidden-import pdfplumber ^
    --hidden-import lxml ^
    --hidden-import lxml.etree ^
    --hidden-import dateutil ^
    gui_main.py

echo.
echo 4. 创建发布包...
if not exist "release" mkdir "release"
copy "dist\发票重命名工具.exe" "release\"
copy "README.md" "release\"

echo.
echo 5. 创建示例目录...
if not exist "release\input" mkdir "release\input"
if not exist "release\output" mkdir "release\output"

echo 创建示例子文件夹...
if not exist "release\input\张三" mkdir "release\input\张三"
if not exist "release\input\李四" mkdir "release\input\李四"

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 可执行文件位置: release\发票重命名工具.exe
echo.
echo 使用说明:
echo 1. 将 release 文件夹复制到目标电脑
echo 2. 在 input 目录中创建以人名命名的子文件夹
echo 3. 将PDF和XML文件放入对应的子文件夹
echo 4. 双击运行 发票重命名工具.exe
echo.
pause
